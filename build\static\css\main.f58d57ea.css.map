{"version": 3, "file": "static/css/main.f58d57ea.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAClC,kBAAmB,CALnB,qJAEY,CAHZ,QASF,CAEA,KACE,uEAEF,CAMA,KACE,sBACF,CAGA,oBACE,SACF,CAEA,0BACE,oBACF,CAEA,0BACE,oBAAmC,CACnC,iBACF,CAEA,gCACE,oBACF,CAGA,YACE,oBAAmC,CACnC,UACF,CAEA,iBACE,oBAAmC,CACnC,UACF,CCpDA,EAUE,0BAA2B,CAC3B,uCAAwC,CARxC,qBAAsB,CAFtB,QAAS,CACT,SAUF,CAEA,OATE,wBAAyB,CAGzB,gBAiBF,CAXA,KAEE,8DAA0E,CAC1E,aAAc,CAFd,wCAA4C,CAG5C,gBAAiB,CACjB,iBAMF,CAGA,IACE,sBAAuB,CACvB,qBAAsB,CACtB,mBAAoB,CACpB,iBAAkB,CAClB,cAAe,CACf,mBAKF,CAGA,mCAPE,wBAAyB,CAGzB,gBASF,CAQA,mBALE,gBAAiB,CACjB,iBAaF,CATA,cAIE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CAGvB,eAAgB,CAFhB,YAGF,CAEA,gBAEE,kBAAmB,CAEnB,iBAAkB,CAHlB,iBAAkB,CAElB,SAEF,CAEA,YASE,sDAAuD,CANvD,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAQhB,kBAAmB,CAFnB,oBAAqB,CAJrB,oEAOF,CAEA,UAEE,aAAc,CADd,gBAAiB,CAEjB,eAAgB,CAGhB,eAAgB,CAFhB,kBAAmB,CACnB,gCAEF,CAEA,aAEE,aAAc,CADd,gBAAiB,CAGjB,eAAgB,CAEhB,kBAAwB,CAHxB,eAAgB,CAEhB,iBAAkB,CAElB,+BACF,CAEA,iBAOE,kCAA2B,CAA3B,0BAA2B,CAJ3B,sDAA8F,CAC9F,0BAAyC,CACzC,kBAAmB,CACnB,oBAAqB,CALrB,eAAgB,CAChB,iBAMF,CAEA,eACE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAChB,8BACF,CAGA,qBAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,wDAA2D,CAE3D,gBAAiB,CAGjB,cAAe,CAFf,UAAW,CACX,SAEF,CAGA,iBAQE,kCAA2B,CAA3B,0BAA2B,CAP3B,8BAAgD,CAChD,0BAAyC,CACzC,kBAAmB,CAUnB,2EAGyC,CAJzC,aAAc,CALd,cAAe,CAGf,eAAgB,CANhB,cAAe,CAKf,iBAAkB,CAJlB,eAAgB,CAMhB,oBAAqB,CALrB,oDAWF,CAEA,wBAOE,uDAAqF,CANrF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAKX,mBAAoB,CAPpB,iBAAkB,CAClB,KAAM,CAKN,mBAAqB,CAHrB,UAKF,CAEA,8BACE,SACF,CAEA,uBAEE,sBAAqC,CACrC,0EAGwC,CALxC,2BAMF,CAEA,WAME,wDAAiF,CADjF,WAAY,CAFZ,SAAU,CAIV,SAAU,CAEV,mBAAoB,CARpB,iBAAkB,CAClB,QAAS,CAMT,2BAA6B,CAJ7B,UAMF,CAEA,kCACE,SACF,CAEA,YAME,uDAAqF,CADrF,UAAW,CAFX,MAAO,CAIP,UAAY,CANZ,iBAAkB,CAGlB,OAAQ,CAFR,KAMF,CAGA,uBAEE,kBAAmB,CAGnB,iCAAgD,CAJhD,YAAa,CAEb,kBAAmB,CACnB,qBAEF,CAEA,mBAGE,aAAc,CAId,4CAAiD,CANjD,cAAe,CACf,mBAAoB,CAEpB,iDAEkC,CAElC,uBACF,CAEA,0CACE,iDAEkC,CAClC,qBACF,CAEA,sBACE,QACF,CAEA,oBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAIhB,eAAgB,CAFhB,mBAAqB,CACrB,gCAEF,CAEA,oBAEE,aAAc,CADd,gBAAiB,CAEjB,eAAgB,CAChB,UACF,CAEA,gBAEE,aAAe,CADf,oBAEF,CAEA,YAGE,kBAAmB,CAGnB,iCAAkD,CALlD,YAAa,CACb,6BAA8B,CAE9B,mBAAqB,CACrB,eAEF,CAEA,uBACE,kBACF,CAEA,cAEE,aAAc,CADd,gBAAkB,CAElB,eAAgB,CAChB,UACF,CAEA,cAEE,aAAc,CADd,gBAAkB,CAElB,eAAgB,CAChB,gBAAiB,CACjB,8BACF,CAEA,oBAME,oBAA8B,CAK9B,0BAA2C,CAJ3C,kBAAmB,CALnB,aAAc,CADd,cAAe,CAEf,iBAAkB,CAMlB,eAAgB,CALhB,oBAAqB,CACrB,YAOF,CAEA,iCAJE,iCAA0B,CAA1B,yBAeF,CAXA,aAGE,kBAAmB,CAInB,oBAA+B,CAF/B,8BAA6C,CAJ7C,YAAa,CACb,6BAA8B,CAM9B,2BAAoC,CACpC,mBAEF,CAEA,aACE,cAAe,CAEf,eAEF,CAEA,2BALE,aAAc,CAEd,8BAQF,CALA,cACE,gBAAiB,CAEjB,6BAEF,CAEA,qCACE,yBACF,CAEA,oCACE,8BACF,CAGA,aACE,gBAAiB,CAGjB,eAAgB,CAFhB,YAAa,CACb,iBAEF,CAEA,aAIE,oBAAmC,CACnC,wBAAyB,CAGzB,kBAAmB,CAFnB,aAAc,CAId,wCAA4C,CAC5C,eAAgB,CARhB,SAAU,CAIV,oBAAsB,CANtB,iBAAkB,CAQlB,oBAAqB,CAPrB,QAAS,CAUT,uBAAyB,CACzB,UACF,CAEA,mBACE,oBAAmC,CACnC,0BACF,CAEA,gBAEE,aAAc,CADd,gBAAiB,CAGjB,gBAAiB,CAEjB,iBAAkB,CAHlB,iBAAkB,CAElB,SAEF,CAEA,cAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,kBAAmB,CACnB,8BACF,CAEA,iBAEE,aAAc,CADd,gBAAiB,CAEjB,kBACF,CAEA,mBAME,kCAA2B,CAA3B,0BAA2B,CAL3B,oBAAqC,CACrC,0BAAyC,CACzC,kBAAmB,CAEnB,aAAc,CAEd,gBAAiB,CAHjB,YAAa,CAIb,UACF,CAEA,iBAEE,aAAc,CADd,cAAe,CAEf,oBACF,CAEA,mBAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,eACF,CAEA,8BACE,0BAA4B,CAE5B,eAAgB,CADhB,UAEF,CAMA,oDACE,yBACF,CAGA,mBAQE,eAAgB,CADhB,mBAAoB,CANpB,cAAe,CAKf,SAGF,CAEA,8BANE,WAAY,CAFZ,MAAO,CADP,KAAM,CAEN,UAkBF,CAXA,WAOE,6DAA+D,CAE/D,gBAAiB,CACjB,sBAAuB,CAJvB,WAAa,CALb,iBAAkB,CAOlB,uBAGF,CAGA,8BAEE,6DAA+D,CAD/D,WAAa,CAEb,qBACF,CAGA,0BASE,sDAAuD,CAFvD,kEAA2F,CAN3F,UAAW,CAKX,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAAW,CAGX,SAEF,CAaA,SAEE,aAAc,CACd,iBACF,CAEA,gBALE,gBAQF,CAHA,OACE,aAEF,CAGA,sBACE,GACE,oEAIF,CACA,IACE,oEAIF,CACA,GACE,oEAIF,CACF,CAEA,gBACE,GACE,8BACF,CACA,GACE,iDACF,CACF,CAEA,wBACE,MACE,oCACF,CACA,IACE,wCACF,CACA,IACE,wCACF,CACA,IACE,yCACF,CACF,CAEA,qBACE,8CACF,CAEA,mCACE,mBACF,CAEA,mCACE,qBACF,CAEA,mCACE,qBACF,CAEA,iBACE,MACE,uBACF,CACA,IACE,2BACF,CACF,CAEA,UACE,uCACF,CAGA,qBAEE,KAMF,CAGA,sCANE,WAAY,CAFZ,MAAO,CAIP,mBAAoB,CANpB,cAAe,CAGf,UAAW,CAEX,SAaF,CARA,iBAEE,QAMF,CAEA,OAOE,sCAAuC,CAFvC,wDAAgF,CAChF,iBAAkB,CAJlB,YAAa,CAEb,YAAa,CAHb,iBAAkB,CAElB,WAKF,CAEA,qBACE,GAEE,UAAY,CADZ,gCAEF,CACA,GAEE,SAAU,CADV,qCAEF,CACF,CAKA,0BACE,cACE,cACF,CAEA,aAEE,UAAW,CADX,wDAEF,CAEA,YACE,cACF,CAEA,cACE,gBACF,CACF,CAGA,yBAEE,WACE,WACF,CAEA,YACE,gBAAiB,CAEjB,kBAAmB,CADnB,kBAEF,CAEA,UACE,gBAAiB,CACjB,oBAAqB,CACrB,cACF,CAEA,aACE,cAAe,CAEf,aAAc,CADd,gBAEF,CAEA,iBACE,oBAA0B,CAC1B,oBACF,CAEA,eACE,cACF,CAEA,qBAEE,UAAW,CADX,yBAA0B,CAE1B,cACF,CAEA,iBACE,mBACF,CAEA,aACE,yBAAkC,CAClC,mBACF,CAEA,uBACE,qBAAsB,CAEtB,oBAAqB,CADrB,iBAEF,CAEA,mBACE,gBAAiB,CAEjB,kBAAmB,CADnB,cAEF,CAEA,oBACE,gBACF,CAEA,oBACE,cACF,CAEA,YACE,qBAAsB,CAEtB,SACF,CAEA,0BAJE,iBAMF,CAEA,oBACE,gBAAkB,CAClB,iBACF,CAEA,cACE,gBAAiB,CACjB,mBACF,CAEA,iBACE,gBAAiB,CACjB,kBACF,CAEA,mBAEE,eAAgB,CADhB,mBAEF,CAEA,iBACE,gBAAiB,CACjB,kBACF,CAEA,mBACE,gBAAiB,CACjB,eACF,CAEA,aAIE,eAAiB,CAFjB,SAAU,CACV,kBAAoB,CAFpB,QAIF,CAEA,gBACE,kBACF,CACF,CAGA,yBAKE,2BACE,kBACF,CAGA,WACE,WACF,CAEA,YACE,gBAAiB,CACjB,eACF,CAEA,UACE,gBAAiB,CACjB,eACF,CAEA,aACE,gBAAkB,CAClB,eACF,CAEA,iBACE,mBAA4B,CAC5B,kBACF,CAEA,eACE,eACF,CAEA,qBAEE,QAAS,CADT,yBAA0B,CAE1B,eACF,CAEA,iBACE,mBACF,CAEA,aACE,yBAAgC,CAChC,YACF,CAEA,mBACE,cAAe,CACf,mBACF,CAEA,oBACE,gBACF,CAEA,oBACE,eACF,CAEA,gBACE,kBACF,CAEA,YACE,mBAAqB,CACrB,eACF,CAEA,4BACE,gBACF,CAEA,oBACE,eAAiB,CAEjB,kBAAmB,CADnB,aAEF,CAEA,aACE,iBACF,CAEA,aACE,eACF,CAEA,cACE,cAAe,CACf,eACF,CAEA,iBACE,gBAAiB,CACjB,oBACF,CAEA,mBAEE,aAAc,CADd,mBAEF,CAEA,iBACE,gBACF,CAEA,mBACE,cAAe,CACf,eACF,CAEA,aAIE,gBAAkB,CAFlB,UAAY,CACZ,mBAAsB,CAFtB,SAIF,CAEA,gBACE,gBACF,CAEA,UACE,2BACF,CAEA,cAEE,yBAA4B,CAD5B,4BAEF,CAEA,mBACE,yBAA2B,CAC3B,wBACF,CAEA,qBACE,wBACF,CACF,CAGA,yBACE,YACE,gBACF,CAEA,aACE,SACF,CAEA,aACE,kBACF,CAEA,aACE,cACF,CAEA,aACE,eACF,CAEA,gBACE,eACF,CAEA,cACE,gBACF,CAEA,mBACE,oBACF,CACF,CAGA,sDACE,cACE,YACF,CAEA,YACE,cAAe,CACf,mBACF,CAEA,UACE,cAAe,CACf,kBACF,CAEA,aACE,eACF,CAEA,aACE,SACF,CAEA,aACE,YACF,CACF,CAGA,yCACE,aACE,6BACF,CAEA,oBACE,oBACF,CAMA,yCACE,oBACF,CACF", "sources": ["index.css", "App.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: 'Noto Sans Sinhala', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background: #0a0a0a;\n  color: #f4d03f;\n  overflow-x: hidden;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n* {\n  box-sizing: border-box;\n}\n\nhtml {\n  scroll-behavior: smooth;\n}\n\n/* Custom scrollbar */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: rgba(26, 26, 46, 0.5);\n}\n\n::-webkit-scrollbar-thumb {\n  background: rgba(244, 208, 63, 0.3);\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: rgba(244, 208, 63, 0.5);\n}\n\n/* Selection styles */\n::selection {\n  background: rgba(244, 208, 63, 0.3);\n  color: #fff;\n}\n\n::-moz-selection {\n  background: rgba(244, 208, 63, 0.3);\n  color: #fff;\n}", "* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n  /* Disable text selection */\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  /* Disable right-click context menu */\n  -webkit-touch-callout: none;\n  -webkit-tap-highlight-color: transparent;\n}\n\nbody {\n  font-family: 'Noto Sans Sinhala', sans-serif;\n  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);\n  color: #f4d03f;\n  min-height: 100vh;\n  overflow-x: hidden;\n  /* Additional protection */\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n/* Disable image dragging and selection */\nimg {\n  -webkit-user-drag: none;\n  -khtml-user-drag: none;\n  -moz-user-drag: none;\n  -o-user-drag: none;\n  user-drag: none;\n  pointer-events: none;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n/* Disable selection on all text elements */\np, h1, h2, h3, h4, h5, h6, span, div, a {\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n.App {\n  min-height: 100vh;\n  position: relative;\n}\n\n/* Landing Page Styles */\n.landing-page {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 2rem;\n  position: relative;\n  overflow: hidden;\n}\n\n.landing-header {\n  text-align: center;\n  margin-bottom: 4rem;\n  z-index: 2;\n  position: relative;\n}\n\n.main-title {\n  font-size: 3.8rem;\n  font-weight: 700;\n  color: #f4d03f;\n  text-shadow:\n    0 0 20px rgba(244, 208, 63, 0.6),\n    0 0 40px rgba(244, 208, 63, 0.4),\n    0 0 60px rgba(244, 208, 63, 0.2);\n  margin-bottom: 1.5rem;\n  animation: divineGlow 3s ease-in-out infinite alternate;\n  letter-spacing: 2px;\n}\n\n.subtitle {\n  font-size: 1.8rem;\n  color: #e8f4fd;\n  font-weight: 400;\n  margin-bottom: 2rem;\n  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\n  line-height: 1.4;\n}\n\n.description {\n  font-size: 1.2rem;\n  color: #d5dbdb;\n  max-width: 700px;\n  line-height: 1.8;\n  text-align: center;\n  margin: 0 auto 2rem auto;\n  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);\n}\n\n.divine-blessing {\n  margin-top: 2rem;\n  padding: 1rem 2rem;\n  background: linear-gradient(135deg, rgba(244, 208, 63, 0.1) 0%, rgba(244, 208, 63, 0.05) 100%);\n  border: 1px solid rgba(244, 208, 63, 0.3);\n  border-radius: 25px;\n  display: inline-block;\n  backdrop-filter: blur(10px);\n}\n\n.blessing-text {\n  color: #f4d03f;\n  font-size: 1.1rem;\n  font-weight: 500;\n  text-shadow: 0 0 10px rgba(244, 208, 63, 0.5);\n}\n\n/* Premium Zodiac Grid */\n.premium-zodiac-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n  gap: 2.5rem;\n  max-width: 1400px;\n  width: 100%;\n  z-index: 2;\n  padding: 0 1rem;\n}\n\n/* Dark Glass Card Design - Consistent with other components */\n.dark-glass-card {\n  background: rgba(255, 255, 255, 0.02) !important;\n  border: 1px solid rgba(244, 208, 63, 0.3);\n  border-radius: 20px;\n  padding: 2.5rem;\n  text-align: left;\n  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n  cursor: pointer;\n  backdrop-filter: blur(15px);\n  position: relative;\n  overflow: hidden;\n  text-decoration: none;\n  color: inherit;\n  box-shadow:\n    0 8px 32px rgba(0, 0, 0, 0.3),\n    0 2px 8px rgba(244, 208, 63, 0.1),\n    inset 0 1px 0 rgba(255, 255, 255, 0.05);\n}\n\n.dark-glass-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(244, 208, 63, 0.1), transparent);\n  transition: left 0.5s;\n  pointer-events: none;\n}\n\n.dark-glass-card:hover::before {\n  left: 100%;\n}\n\n.dark-glass-card:hover {\n  transform: translateY(-10px);\n  border-color: rgba(244, 208, 63, 0.5);\n  box-shadow:\n    0 20px 60px rgba(0, 0, 0, 0.4),\n    0 10px 30px rgba(244, 208, 63, 0.2),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\n}\n\n.card-glow {\n  position: absolute;\n  top: -50%;\n  left: -50%;\n  width: 200%;\n  height: 200%;\n  background: radial-gradient(circle, rgba(244, 208, 63, 0.08) 0%, transparent 70%);\n  opacity: 0;\n  transition: opacity 0.4s ease;\n  pointer-events: none;\n}\n\n.dark-glass-card:hover .card-glow {\n  opacity: 1;\n}\n\n.card-shine {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background: linear-gradient(90deg, transparent, rgba(244, 208, 63, 0.6), transparent);\n  opacity: 0.7;\n}\n\n/* Premium Card Content Styles */\n.zodiac-header-section {\n  display: flex;\n  align-items: center;\n  margin-bottom: 2rem;\n  padding-bottom: 1.5rem;\n  border-bottom: 1px solid rgba(244, 208, 63, 0.2);\n}\n\n.zodiac-icon-large {\n  font-size: 4rem;\n  margin-right: 1.5rem;\n  color: #f4d03f;\n  text-shadow:\n    0 0 20px rgba(244, 208, 63, 0.6),\n    0 0 40px rgba(244, 208, 63, 0.4);\n  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));\n  transition: all 0.3s ease;\n}\n\n.dark-glass-card:hover .zodiac-icon-large {\n  text-shadow:\n    0 0 30px rgba(244, 208, 63, 0.8),\n    0 0 60px rgba(244, 208, 63, 0.6);\n  transform: scale(1.05);\n}\n\n.zodiac-names-section {\n  flex: 1;\n}\n\n.sinhala-name-large {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #f4d03f;\n  margin-bottom: 0.5rem;\n  text-shadow: 0 2px 10px rgba(244, 208, 63, 0.3);\n  line-height: 1.2;\n}\n\n.english-name-small {\n  font-size: 1.1rem;\n  color: #d5dbdb;\n  font-weight: 400;\n  opacity: 0.9;\n}\n\n.zodiac-details {\n  margin-bottom: 1.5rem;\n  space-y: 0.8rem;\n}\n\n.detail-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.8rem;\n  padding: 0.6rem 0;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.05);\n}\n\n.detail-row:last-child {\n  border-bottom: none;\n}\n\n.detail-label {\n  font-size: 0.95rem;\n  color: #aeb6bf;\n  font-weight: 500;\n  opacity: 0.9;\n}\n\n.detail-value {\n  font-size: 0.95rem;\n  color: #f4d03f;\n  font-weight: 600;\n  text-align: right;\n  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);\n}\n\n.zodiac-description {\n  font-size: 1rem;\n  color: #d5dbdb;\n  font-style: italic;\n  margin-bottom: 1.5rem;\n  padding: 1rem;\n  background: rgba(0, 0, 0, 0.1);\n  border-radius: 10px;\n  border-left: 3px solid rgba(244, 208, 63, 0.4);\n  line-height: 1.4;\n  backdrop-filter: blur(5px);\n  border: 1px solid rgba(255, 255, 255, 0.05);\n}\n\n.card-action {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 1rem;\n  border-top: 1px solid rgba(244, 208, 63, 0.2);\n  margin-top: auto;\n  background: rgba(0, 0, 0, 0.05);\n  margin: 1rem -2.5rem -2.5rem -2.5rem;\n  padding: 1rem 2.5rem;\n  backdrop-filter: blur(5px);\n}\n\n.action-text {\n  font-size: 1rem;\n  color: #f4d03f;\n  font-weight: 600;\n  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);\n}\n\n.action-arrow {\n  font-size: 1.2rem;\n  color: #f4d03f;\n  transition: transform 0.3s ease;\n  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);\n}\n\n.dark-glass-card:hover .action-arrow {\n  transform: translateX(5px);\n}\n\n.dark-glass-card:hover .action-text {\n  text-shadow: 0 0 15px rgba(244, 208, 63, 0.5);\n}\n\n/* Zodiac Page Styles */\n.zodiac-page {\n  min-height: 100vh;\n  padding: 2rem;\n  position: relative;\n  overflow: hidden;\n}\n\n.back-button {\n  position: absolute;\n  top: 2rem;\n  left: 2rem;\n  background: rgba(244, 208, 63, 0.1);\n  border: 1px solid #f4d03f;\n  color: #f4d03f;\n  padding: 0.8rem 1.5rem;\n  border-radius: 25px;\n  text-decoration: none;\n  font-family: 'Noto Sans Sinhala', sans-serif;\n  font-weight: 500;\n  transition: all 0.3s ease;\n  z-index: 10;\n}\n\n.back-button:hover {\n  background: rgba(244, 208, 63, 0.2);\n  transform: translateX(-5px);\n}\n\n.zodiac-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  text-align: center;\n  padding-top: 4rem;\n  z-index: 2;\n  position: relative;\n}\n\n.zodiac-title {\n  font-size: 4rem;\n  font-weight: 700;\n  color: #f4d03f;\n  margin-bottom: 1rem;\n  text-shadow: 0 0 30px rgba(244, 208, 63, 0.6);\n}\n\n.zodiac-subtitle {\n  font-size: 1.5rem;\n  color: #d5dbdb;\n  margin-bottom: 3rem;\n}\n\n.horoscope-section {\n  background: rgba(255, 255, 255, 0.02);\n  border: 1px solid rgba(244, 208, 63, 0.3);\n  border-radius: 20px;\n  padding: 3rem;\n  margin: 2rem 0;\n  backdrop-filter: blur(10px);\n  max-width: 1200px;\n  width: 100%;\n}\n\n.horoscope-title {\n  font-size: 2rem;\n  color: #f4d03f;\n  margin-bottom: 1.5rem;\n}\n\n.horoscope-content {\n  font-size: 1.2rem;\n  line-height: 1.8;\n  color: #d5dbdb;\n  text-align: left;\n}\n\n.structured-horoscope-display {\n  max-width: 1200px !important;\n  width: 100%;\n  text-align: left;\n}\n\n.horoscope-category-card {\n  text-align: left !important;\n}\n\n.horoscope-category-card p {\n  text-align: left !important;\n}\n\n/* Divine Background Image Styles */\n.divine-background {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 0;\n  pointer-events: none;\n  overflow: hidden;\n}\n\n.god-image {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  opacity: 0.08;\n  filter: blur(0.5px) sepia(20%) saturate(150%) hue-rotate(30deg);\n  transition: all 0.3s ease;\n  object-fit: cover;\n  object-position: center;\n}\n\n/* Enhance the divine presence on hover */\n.zodiac-page:hover .god-image {\n  opacity: 0.12;\n  filter: blur(0.3px) sepia(25%) saturate(160%) hue-rotate(30deg);\n  transform: scale(1.02);\n}\n\n/* Additional divine glow effect */\n.divine-background::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: radial-gradient(circle at center, rgba(244, 208, 63, 0.03) 0%, transparent 70%);\n  z-index: 1;\n  animation: divineGlow 4s ease-in-out infinite alternate;\n}\n\n@keyframes divineGlow {\n  0% {\n    opacity: 0.3;\n    transform: scale(1);\n  }\n  100% {\n    opacity: 0.6;\n    transform: scale(1.05);\n  }\n}\n\n.loading {\n  font-size: 1.1rem;\n  color: #aeb6bf;\n  font-style: italic;\n}\n\n.error {\n  color: #e74c3c;\n  font-size: 1.1rem;\n}\n\n/* Animations */\n@keyframes divineGlow {\n  0% {\n    text-shadow:\n      0 0 20px rgba(244, 208, 63, 0.6),\n      0 0 40px rgba(244, 208, 63, 0.4),\n      0 0 60px rgba(244, 208, 63, 0.2);\n  }\n  50% {\n    text-shadow:\n      0 0 30px rgba(244, 208, 63, 0.8),\n      0 0 60px rgba(244, 208, 63, 0.6),\n      0 0 90px rgba(244, 208, 63, 0.4);\n  }\n  100% {\n    text-shadow:\n      0 0 20px rgba(244, 208, 63, 0.6),\n      0 0 40px rgba(244, 208, 63, 0.4),\n      0 0 60px rgba(244, 208, 63, 0.2);\n  }\n}\n\n@keyframes glow {\n  from {\n    text-shadow: 0 0 20px rgba(244, 208, 63, 0.5);\n  }\n  to {\n    text-shadow: 0 0 30px rgba(244, 208, 63, 0.8), 0 0 40px rgba(244, 208, 63, 0.6);\n  }\n}\n\n@keyframes premiumFloat {\n  0%, 100% {\n    transform: translateY(0px) rotate(0deg);\n  }\n  25% {\n    transform: translateY(-5px) rotate(0.5deg);\n  }\n  50% {\n    transform: translateY(-10px) rotate(0deg);\n  }\n  75% {\n    transform: translateY(-5px) rotate(-0.5deg);\n  }\n}\n\n.premium-zodiac-card {\n  animation: premiumFloat 6s ease-in-out infinite;\n}\n\n.premium-zodiac-card:nth-child(even) {\n  animation-delay: -3s;\n}\n\n.premium-zodiac-card:nth-child(3n) {\n  animation-delay: -1.5s;\n}\n\n.premium-zodiac-card:nth-child(4n) {\n  animation-delay: -4.5s;\n}\n\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n}\n\n.floating {\n  animation: float 3s ease-in-out infinite;\n}\n\n/* Particles Background */\n.particles-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n  pointer-events: none;\n}\n\n/* Smoke Animation */\n.smoke-container {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  z-index: 1;\n}\n\n.smoke {\n  position: absolute;\n  bottom: -50px;\n  width: 100px;\n  height: 100px;\n  background: radial-gradient(circle, rgba(244, 208, 63, 0.1) 0%, transparent 70%);\n  border-radius: 50%;\n  animation: smokeRise 8s linear infinite;\n}\n\n@keyframes smokeRise {\n  0% {\n    transform: translateY(0) scale(1);\n    opacity: 0.7;\n  }\n  100% {\n    transform: translateY(-100vh) scale(2);\n    opacity: 0;\n  }\n}\n\n/* Enhanced Mobile Responsive Design */\n\n/* Tablet styles */\n@media (max-width: 1024px) {\n  .landing-page {\n    padding: 1.5rem;\n  }\n  \n  .zodiac-grid {\n    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\n    gap: 1.5rem;\n  }\n  \n  .main-title {\n    font-size: 3rem;\n  }\n  \n  .zodiac-title {\n    font-size: 3.5rem;\n  }\n}\n\n/* Mobile landscape and small tablets */\n@media (max-width: 768px) {\n  /* Adjust divine background for tablets */\n  .god-image {\n    opacity: 0.15;\n  }\n  \n  .main-title {\n    font-size: 2.8rem;\n    margin-bottom: 1rem;\n    letter-spacing: 1px;\n  }\n\n  .subtitle {\n    font-size: 1.4rem;\n    margin-bottom: 1.5rem;\n    padding: 0 1rem;\n  }\n\n  .description {\n    font-size: 1rem;\n    padding: 0 1.5rem;\n    max-width: 90%;\n  }\n\n  .divine-blessing {\n    margin: 1.5rem 1rem 0 1rem;\n    padding: 0.8rem 1.5rem;\n  }\n\n  .blessing-text {\n    font-size: 1rem;\n  }\n\n  .premium-zodiac-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n    padding: 0 1rem;\n  }\n\n  .dark-glass-card {\n    padding: 2rem 1.5rem;\n  }\n\n  .card-action {\n    margin: 1rem -1.5rem -2rem -1.5rem;\n    padding: 1rem 1.5rem;\n  }\n\n  .zodiac-header-section {\n    flex-direction: column;\n    text-align: center;\n    margin-bottom: 1.5rem;\n  }\n\n  .zodiac-icon-large {\n    font-size: 3.5rem;\n    margin-right: 0;\n    margin-bottom: 1rem;\n  }\n\n  .sinhala-name-large {\n    font-size: 1.8rem;\n  }\n\n  .english-name-small {\n    font-size: 1rem;\n  }\n\n  .detail-row {\n    flex-direction: column;\n    text-align: center;\n    gap: 0.3rem;\n  }\n\n  .detail-value {\n    text-align: center;\n  }\n\n  .zodiac-description {\n    font-size: 0.95rem;\n    text-align: center;\n  }\n  \n  .zodiac-title {\n    font-size: 2.5rem;\n    margin-bottom: 0.8rem;\n  }\n  \n  .zodiac-subtitle {\n    font-size: 1.3rem;\n    margin-bottom: 2rem;\n  }\n  \n  .horoscope-section {\n    padding: 2rem 1.5rem;\n    margin: 1.5rem 0;\n  }\n  \n  .horoscope-title {\n    font-size: 1.8rem;\n    margin-bottom: 1rem;\n  }\n  \n  .horoscope-content {\n    font-size: 1.1rem;\n    line-height: 1.7;\n  }\n  \n  .back-button {\n    top: 1rem;\n    left: 1rem;\n    padding: 0.6rem 1rem;\n    font-size: 0.9rem;\n  }\n  \n  .zodiac-content {\n    padding-top: 3.5rem;\n  }\n}\n\n/* Mobile portrait */\n@media (max-width: 480px) {\n  .landing-page {\n    padding: 1rem 0.5rem;\n  }\n  \n  .zodiac-page {\n    padding: 1rem 0.5rem;\n  }\n  \n  /* Adjust divine background for mobile */\n  .god-image {\n    opacity: 0.12;\n  }\n  \n  .main-title {\n    font-size: 2.2rem;\n    line-height: 1.2;\n  }\n\n  .subtitle {\n    font-size: 1.2rem;\n    padding: 0 0.5rem;\n  }\n\n  .description {\n    font-size: 0.95rem;\n    padding: 0 0.5rem;\n  }\n\n  .divine-blessing {\n    margin: 1rem 0.5rem 0 0.5rem;\n    padding: 0.6rem 1rem;\n  }\n\n  .blessing-text {\n    font-size: 0.9rem;\n  }\n\n  .premium-zodiac-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n    padding: 0 0.5rem;\n  }\n\n  .dark-glass-card {\n    padding: 1.5rem 1rem;\n  }\n\n  .card-action {\n    margin: 1rem -1rem -1.5rem -1rem;\n    padding: 1rem;\n  }\n\n  .zodiac-icon-large {\n    font-size: 3rem;\n    margin-bottom: 0.8rem;\n  }\n\n  .sinhala-name-large {\n    font-size: 1.5rem;\n  }\n\n  .english-name-small {\n    font-size: 0.9rem;\n  }\n\n  .zodiac-details {\n    margin-bottom: 1rem;\n  }\n\n  .detail-row {\n    margin-bottom: 0.6rem;\n    padding: 0.3rem 0;\n  }\n\n  .detail-label, .detail-value {\n    font-size: 0.85rem;\n  }\n\n  .zodiac-description {\n    font-size: 0.9rem;\n    padding: 0.8rem;\n    margin-bottom: 1rem;\n  }\n\n  .card-action {\n    padding-top: 0.8rem;\n  }\n\n  .action-text {\n    font-size: 0.9rem;\n  }\n  \n  .zodiac-title {\n    font-size: 2rem;\n    line-height: 1.2;\n  }\n  \n  .zodiac-subtitle {\n    font-size: 1.1rem;\n    margin-bottom: 1.5rem;\n  }\n  \n  .horoscope-section {\n    padding: 1.5rem 1rem;\n    margin: 1rem 0;\n  }\n  \n  .horoscope-title {\n    font-size: 1.5rem;\n  }\n  \n  .horoscope-content {\n    font-size: 1rem;\n    line-height: 1.6;\n  }\n  \n  .back-button {\n    top: 0.8rem;\n    left: 0.8rem;\n    padding: 0.5rem 0.8rem;\n    font-size: 0.85rem;\n  }\n  \n  .zodiac-content {\n    padding-top: 3rem;\n  }\n  \n  .controls {\n    margin-top: 1.5rem !important;\n  }\n  \n  .sound-toggle {\n    padding: 0.6rem 1rem !important;\n    font-size: 0.9rem !important;\n  }\n  \n  .spiritual-message {\n    margin-top: 2rem !important;\n    padding: 1.5rem !important;\n  }\n  \n  .spiritual-message p {\n    font-size: 1rem !important;\n  }\n}\n\n/* Very small screens */\n@media (max-width: 360px) {\n  .main-title {\n    font-size: 1.8rem;\n  }\n  \n  .zodiac-grid {\n    gap: 0.6rem;\n  }\n  \n  .zodiac-card {\n    padding: 1rem 0.6rem;\n  }\n  \n  .zodiac-icon {\n    font-size: 2rem;\n  }\n  \n  .zodiac-name {\n    font-size: 0.9rem;\n  }\n  \n  .zodiac-english {\n    font-size: 0.8rem;\n  }\n  \n  .zodiac-title {\n    font-size: 1.8rem;\n  }\n  \n  .horoscope-section {\n    padding: 1.2rem 0.8rem;\n  }\n}\n\n/* Landscape orientation adjustments */\n@media (max-height: 500px) and (orientation: landscape) {\n  .landing-page {\n    padding: 1rem;\n  }\n  \n  .main-title {\n    font-size: 2rem;\n    margin-bottom: 0.5rem;\n  }\n  \n  .subtitle {\n    font-size: 1rem;\n    margin-bottom: 1rem;\n  }\n  \n  .description {\n    font-size: 0.9rem;\n  }\n  \n  .zodiac-grid {\n    gap: 0.8rem;\n  }\n  \n  .zodiac-card {\n    padding: 1rem;\n  }\n}\n\n/* Touch-friendly improvements */\n@media (hover: none) and (pointer: coarse) {\n  .zodiac-card {\n    transition: transform 0.2s ease;\n  }\n  \n  .zodiac-card:active {\n    transform: scale(0.98);\n  }\n  \n  .back-button:active {\n    transform: scale(0.95);\n  }\n  \n  .sound-toggle:active {\n    transform: scale(0.95);\n  }\n}"], "names": [], "sourceRoot": ""}