import React, { useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import ParticleBackground from './ParticleBackground';
import KuberaAnimation from './KuberaAnimation';

const zodiacIcons = {
  aries: '♈',
  taurus: '♉',
  gemini: '♊',
  cancer: '♋',
  leo: '♌',
  virgo: '♍',
  libra: '♎',
  scorpio: '♏',
  sagittarius: '♐',
  capricorn: '♑',
  aquarius: '♒',
  pisces: '♓'
};

// Enhanced zodiac data with additional information
const zodiacData = {
  aries: {
    dates: 'මාර්තු 21 - අප්‍රේල් 19',
    element: 'ගිනි',
    planet: 'අඟහරු',
    color: 'රතු',
    gemstone: 'දියමන්ති',
    description: 'නායකත්ව ගුණාංග සහිත'
  },
  taurus: {
    dates: 'අප්‍රේල් 20 - මැයි 20',
    element: 'පෘථිවි',
    planet: 'සිකුරු',
    color: 'කොළ',
    gemstone: 'මරකත',
    description: 'ස්ථිර සහ විශ්වාසදායක'
  },
  gemini: {
    dates: 'මැයි 21 - ජූනි 20',
    element: 'වායු',
    planet: 'බුධ',
    color: 'කහ',
    gemstone: 'පීත මණි',
    description: 'බුද්ධිමත් සහ කතාබහට ප්‍රිය'
  },
  cancer: {
    dates: 'ජූනි 21 - ජූලි 22',
    element: 'ජල',
    planet: 'චන්ද්‍රයා',
    color: 'රිදී',
    gemstone: 'මුතු',
    description: 'සංවේදී සහ රැකවරණ ගුණ'
  },
  leo: {
    dates: 'ජූලි 23 - අගෝස්තු 22',
    element: 'ගිනි',
    planet: 'සූර්යයා',
    color: 'රන්වන්',
    gemstone: 'රුබි',
    description: 'ආත්මවිශ්වාසී සහ උදාර'
  },
  virgo: {
    dates: 'අගෝස්තු 23 - සැප්තැම්බර් 22',
    element: 'පෘථිවි',
    planet: 'බුධ',
    color: 'නිල්',
    gemstone: 'නිල්මණි',
    description: 'විශ්ලේෂණාත්මක සහ ක්‍රමවත්'
  },
  libra: {
    dates: 'සැප්තැම්බර් 23 - ඔක්තෝබර් 22',
    element: 'වායු',
    planet: 'සිකුරු',
    color: 'රෝස',
    gemstone: 'ඔපල්',
    description: 'සමබර සහ සාධාරණ'
  },
  scorpio: {
    dates: 'ඔක්තෝබර් 23 - නොවැම්බර් 21',
    element: 'ජල',
    planet: 'අඟහරු',
    color: 'තද රතු',
    gemstone: 'ටොපාස්',
    description: 'තීව්‍ර සහ අභිරහස්'
  },
  sagittarius: {
    dates: 'නොවැම්බර් 22 - දෙසැම්බර් 21',
    element: 'ගිනි',
    planet: 'බ්‍රහස්පති',
    color: 'දම්',
    gemstone: 'ටර්කොයිස්',
    description: 'ස්වාධීන සහ ප්‍රීතිමත්'
  },
  capricorn: {
    dates: 'දෙසැම්බර් 22 - ජනවාරි 19',
    element: 'පෘථිවි',
    planet: 'සෙනසුරු',
    color: 'කළු',
    gemstone: 'ගාර්නට්',
    description: 'අධිෂ්ඨානශීලී සහ ප්‍රායෝගික'
  },
  aquarius: {
    dates: 'ජනවාරි 20 - පෙබරවාරි 18',
    element: 'වායු',
    planet: 'යුරේනස්',
    color: 'ටර්කොයිස්',
    gemstone: 'ඇමතිස්ට්',
    description: 'නව්‍ය සහ මානවීය'
  },
  pisces: {
    dates: 'පෙබරවාරි 19 - මාර්තු 20',
    element: 'ජල',
    planet: 'නෙප්චූන්',
    color: 'මුහුදු නිල්',
    gemstone: 'ඇක්වාමරීන්',
    description: 'සංවේදී සහ කලාත්මක'
  }
};

const LandingPage = ({ zodiacSigns }) => {
  useEffect(() => {
    // Add floating animation to zodiac cards with staggered delay
    const cards = document.querySelectorAll('.premium-zodiac-card');
    cards.forEach((card, index) => {
      card.style.animationDelay = `${index * 0.1}s`;
      card.classList.add('floating');
    });
  }, []);

  return (
    <div className="landing-page">
      <ParticleBackground />
      <KuberaAnimation />

      <div className="landing-header">
        <h1 className="main-title">සිංහල ජ්‍යොතිෂ වෙබ් අඩවිය</h1>
        <h2 className="subtitle">කුබේර දෙවියන්ගේ ආශීර්වාදය සමඟ දෛනික රාශිඵල</h2>
        <p className="description">
          පුරාණ ජ්‍යොතිෂ ශාස්ත්‍රයේ ගැඹුරු ඥානය සහ කුබේර දෙවියන්ගේ ආශීර්වාදය සමඟ
          ඔබේ දෛනික රාශිඵල සහ ජීවිත මග පෙන්වීම ලබා ගන්න.
        </p>
        <div className="divine-blessing">
          <span className="blessing-text">🙏 දිව්‍ය ආශීර්වාදය සමඟ 🙏</span>
        </div>
      </div>

      <div className="premium-zodiac-grid">
        {zodiacSigns.map((sign, index) => {
          const signData = zodiacData[sign.id];
          return (
            <Link
              key={sign.id}
              to={`/${sign.id}`}
              className="premium-zodiac-card dark-glass-card"
              style={{
                animationDelay: `${index * 0.1}s`
              }}
            >
              <div className="card-glow"></div>
              <div className="card-shine"></div>

              <div className="zodiac-header-section">
                <div className="zodiac-icon-large">
                  {zodiacIcons[sign.id]}
                </div>
                <div className="zodiac-names-section">
                  <div className="sinhala-name-large">{sign.sinhala}</div>
                  <div className="english-name-small">{sign.english}</div>
                </div>
              </div>

              <div className="zodiac-details">
                <div className="detail-row">
                  <span className="detail-label">කාල සීමාව:</span>
                  <span className="detail-value">{signData?.dates}</span>
                </div>
                <div className="detail-row">
                  <span className="detail-label">මූලද්‍රව්‍යය:</span>
                  <span className="detail-value">{signData?.element}</span>
                </div>
                <div className="detail-row">
                  <span className="detail-label">ග්‍රහයා:</span>
                  <span className="detail-value">{signData?.planet}</span>
                </div>
                <div className="detail-row">
                  <span className="detail-label">මැණික:</span>
                  <span className="detail-value">{signData?.gemstone}</span>
                </div>
              </div>

              <div className="zodiac-description">
                {signData?.description}
              </div>

              <div className="card-action">
                <span className="action-text">රාශිඵල බලන්න</span>
                <span className="action-arrow">→</span>
              </div>
            </Link>
          );
        })}
      </div>
    </div>
  );
};

export default LandingPage;