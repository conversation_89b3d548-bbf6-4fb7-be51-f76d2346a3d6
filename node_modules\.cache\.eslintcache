[{"/mnt/c/Users/<USER>/Desktop/Horoscope/src/index.js": "1", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/App.js": "2", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/ZodiacPage.js": "3", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/LandingPage.js": "4", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/KuberaAnimation.js": "5", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/ParticleBackground.js": "6", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/SmokeAnimation.js": "7", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/services/HoroscopeService.js": "8"}, {"size": 253, "mtime": 1750790409543, "results": "9", "hashOfConfig": "10"}, {"size": 3229, "mtime": 1750790469494, "results": "11", "hashOfConfig": "10"}, {"size": 24122, "mtime": 1750788954826, "results": "12", "hashOfConfig": "10"}, {"size": 2175, "mtime": 1750790474811, "results": "13", "hashOfConfig": "10"}, {"size": 7496, "mtime": 1750614946000, "results": "14", "hashOfConfig": "10"}, {"size": 3837, "mtime": 1750614946000, "results": "15", "hashOfConfig": "10"}, {"size": 2802, "mtime": 1750614946000, "results": "16", "hashOfConfig": "10"}, {"size": 6084, "mtime": 1750790008170, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "p7tm3u", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/mnt/c/Users/<USER>/Desktop/Horoscope/src/index.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/App.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/ZodiacPage.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/LandingPage.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/KuberaAnimation.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/ParticleBackground.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/SmokeAnimation.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/services/HoroscopeService.js", ["42"], [], {"ruleId": "43", "severity": 1, "message": "44", "line": 177, "column": 1, "nodeType": "45", "endLine": 177, "endColumn": 39}, "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration"]